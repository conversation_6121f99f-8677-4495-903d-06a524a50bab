import { Component, OnInit, AfterViewInit, OnDestroy, OnChanges, SimpleChanges, Input } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { ReportDetailsService } from './services/report-details.service';
import { ReportDetail } from 'src/app/shared-services/bonus-measures/model/report-details-model';

// Apply jsPDF plugin
applyPlugin(jsPDF);

@Component({
  selector: 'app-report-details',
  templateUrl: './report-details.component.html',
  styleUrl: './report-details.component.scss'
})
export class ReportDetailsComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @Input() siteId!: string;
  @Input() year!: number;
  @Input() cohortId!: number;
  @Input() locationCd!: string;
  @Input() providerCd!: string;
  @Input() rollingWeek!: number;
  @Input() measuresCd!: string;
  @Input() alertLvl: boolean = true;

  reportDetails: ReportDetail[] = [];
  table!: Tabulator;
  private resizeObserver!: any;
  menuOpen = false;

  // Caching and loading state management
  private cachedData: ReportDetail[] = [];
  private lastQueryParams: string = '';
  isLoading = false;
  hasData = false;

  constructor(
    private reportDetailsService: ReportDetailsService,
    private spinnerService: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    // Don't load data here since inputs might not be set yet
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Load data when any input parameter changes and all required params are available
    if (this.siteId && this.year && this.cohortId && this.locationCd &&
        this.providerCd && this.rollingWeek !== undefined &&
        this.alertLvl !== undefined && this.measuresCd) {

      // Create a unique key for the current parameters
      const currentQueryParams = this.createQueryParamsKey();

      console.log('Input parameters changed, checking cache for:', {
        siteId: this.siteId,
        year: this.year,
        cohortId: this.cohortId,
        locationCd: this.locationCd,
        providerCd: this.providerCd,
        rollingWeek: this.rollingWeek,
        alertLvl: this.alertLvl,
        measuresCd: this.measuresCd
      });

      // Check if this is the same query as the last one (cache hit)
      if (currentQueryParams === this.lastQueryParams && this.cachedData.length > 0) {
        console.log('Cache hit! Using cached data instead of making API call');
        this.reportDetails = [...this.cachedData];
        this.hasData = true;
        this.isLoading = false;

        // Initialize table with cached data
        setTimeout(() => this.initializeTable(), 0);
      } else {
        console.log('Cache miss or new parameters, loading fresh data');
        // Different parameters or no cached data - hide current data and load new
        this.hasData = false;
        this.isLoading = true;
        this.reportDetails = [];

        // Destroy existing table to prevent showing old data
        if (this.table) {
          this.table.destroy();
        }

        this.loadReportDetails();
      }
    }
  }

  ngAfterViewInit(): void {
    // Initialize resize observer for responsive table
    this.initializeResizeObserver();
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.table) {
      this.table.destroy();
    }
  }

  /**
   * Create a unique key for the current query parameters for caching
   */
  private createQueryParamsKey(): string {
    return `${this.siteId}-${this.year}-${this.cohortId}-${this.locationCd}-${this.providerCd}-${this.rollingWeek}-${this.alertLvl}-${this.measuresCd}`;
  }

  /**
   * Load report details data from the service
   */
  loadReportDetails(): void {
    this.spinnerService.show();
    this.isLoading = true;

    const params = {
      siteId: this.siteId,
      year: this.year,
      cohortId: this.cohortId,
      locationCd: this.locationCd,
      providerCd: this.providerCd,
      rollingWeek: this.rollingWeek,
      alertLvl: this.alertLvl,
      measuresCd: this.measuresCd
    };

    this.reportDetailsService.getReportDetails(params).subscribe({
      next: (data) => {
        // Cache the data and update state
        this.reportDetails = data;
        this.cachedData = [...data];
        this.lastQueryParams = this.createQueryParamsKey();
        this.hasData = true;
        this.isLoading = false;

        this.spinnerService.hide();
        console.log(`Loaded ${data.length} report detail records and cached them`);

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading report details data:', error);
        this.isLoading = false;
        this.hasData = false;
        this.spinnerService.hide();
      }
    });
  }

  /**
   * Initialize the resize observer for responsive table behavior
   */
  initializeResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.table) {
          this.table.redraw();
        }
      });

      const tableContainer = document.querySelector('#report-details-table');
      if (tableContainer) {
        this.resizeObserver.observe(tableContainer);
      }
    }
  }

  /**
   * Initialize the Tabulator table with report details data
   */
  initializeTable(): void {
    this.table = new Tabulator("#report-details-table", {
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false,
      },
      data: this.reportDetails,
      printAsHtml: true,
      printStyled: true,
      pagination: true,
      paginationSize: 25,
      paginationSizeSelector: [10, 25, 50, 100],
      height: "90%",
      columnDefaults:{
        resizable: false,
        headerSort: false,
      },      columns: [
        {
          title: "MRN",
          field: "mrn",
          width: 120,
        },
        {
          title: "Name",
          field: "patientName",
          width: 250,
        },
        {
          title: "Birth Date",
          field: "formattedBirthDate",
          width: 120,
          hozAlign: "center"
        },
        {
          title: "Last Visit Date",
          field: "formattedLastVisitDate",
          width: 120,
          hozAlign: "center"
        },
        {
          title: "Recommended Action",
          field: "actionTxt",
          width: 300,
          formatter: "textarea",
        },
        {
          title: "Description",
          field: "detailTxt",
          width: 300,
          formatter: "html",
          cssClass: "detail-cell"
        },
        {
          title: "Comments",
          field: "annotate",
          width: 120,
          hozAlign: "center",
          clickPopup:"Testing"
        }
      ]
    });

    // Subscribe to table events
    this.table.on("tableBuilt", () => {
      console.log("Report details table built successfully");
    });
  }

  /**
   * Toggle the export menu
   */
  toggleMenu(): void {
    this.menuOpen = !this.menuOpen;
  }

  /**
   * Handle clicks outside the menu to close it
   */
  onClickOutsideMenu(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown')) {
      this.menuOpen = false;
    }
  }

  /**
   * Export table data to Excel
   */
  exportToExcel(): void {
    this.table.download("xlsx", "report-details.xlsx", {sheetName: "Report Details"});
  }

  /**
   * Export table data to CSV
   */
  exportToCSV(): void {
    this.table.download("csv", "report-details.csv");
  }

  /**
   * Export table data to PDF
   */
  exportToPDF(): void {
    this.table.download("pdf", "report-details.pdf", {
      orientation: "landscape",
      title: "Report Details"
    });
  }

  /**
   * Print the table
   */
  printTable(): void {
    this.table.print(false, true);
  }

  /**
   * Get total number of patients
   */
  getTotalPatients(): number {
    return this.reportDetails ? this.reportDetails.length : 0;
  }
}
